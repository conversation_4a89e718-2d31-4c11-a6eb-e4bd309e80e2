package tar.service;

import com.atlassian.jira.rest.client.api.IssueRestClient;
import com.atlassian.jira.rest.client.api.JiraRestClient;
import com.atlassian.jira.rest.client.api.JiraRestClientFactory;
import com.atlassian.jira.rest.client.api.domain.BasicIssue;
import com.atlassian.jira.rest.client.api.domain.util.UriUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tar.configuration.JiraConfig;
import tar.model.jira.IssueData;
import tar.model.report.ReportConfig;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

/**
 * Service that interacts with jira.
 */
public class JiraService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JiraService.class);

    private final JiraConfig jiraConfig;
    private final JiraRestClientFactory jiraRestClientFactory;

    private static final String WATCHERS_PATH = "watchers";
    private static final String ATTACHMENTS_PATH = "attachments";

    public JiraService(JiraConfig jiraConfig, JiraRestClientFactory jiraRestClientFactory) {
        this.jiraConfig = jiraConfig;
        this.jiraRestClientFactory = jiraRestClientFactory;
    }

    /**
     * Create an issue and attach a file if present.
     *
     * @param issueData
     * @return
     */
    public BasicIssue createIssue(IssueData issueData) throws URISyntaxException, IOException {
        try (JiraRestClient jiraRestClient = createJiraRestClient()) {
            final IssueRestClient issueClient = jiraRestClient.getIssueClient();
            BasicIssue basicIssue = issueClient.createIssue(issueData.getIssueInput()).claim();
            if (Objects.nonNull(issueData.getAttachment())) {
                InputStream inputStream = new ByteArrayInputStream(issueData.getAttachment().getContent());
                issueClient.addAttachment(getAttachmentUri(basicIssue.getSelf()), inputStream, issueData.getAttachment().getName()).claim();
            }
            if (issueData.getIssueUpdateInput() != null) {
                issueClient.updateIssue(basicIssue.getKey(), issueData.getIssueUpdateInput()).claim();
            }
            return basicIssue;
        }
    }

    public void addWatchers(String issueKey, ReportConfig reportConfig) throws URISyntaxException, IOException {
        List<String> watcherAccountsIds = reportConfig.getIssue().getWatcherAccounts();
        if (watcherAccountsIds != null) {
            try (JiraRestClient jiraRestClient = createJiraRestClient()) {
                final IssueRestClient issueClient = jiraRestClient.getIssueClient();
                BasicIssue issue = issueClient.getIssue(issueKey).claim();

                for (String accountId : watcherAccountsIds) {
                    URI watchersUri = getWatchersUri(issue.getSelf());
                    LOGGER.info("Adding user with accountId: {} to watchers using URI: {}", accountId, watchersUri);
                    issueClient.addWatcher(watchersUri, accountId).claim();
                }
            }
        }
    }

    private URI getAttachmentUri(URI self) {
        return UriUtil.path(self, ATTACHMENTS_PATH);
    }

    private URI getWatchersUri(URI self) {
        return UriUtil.path(self, WATCHERS_PATH);
    }

    private JiraRestClient createJiraRestClient() throws URISyntaxException {
        return jiraRestClientFactory
                .createWithBasicHttpAuthentication(
                    new URI(jiraConfig.getUrl()),
                    jiraConfig.getUsername(),
                    jiraConfig.getPassword());
    }
}
