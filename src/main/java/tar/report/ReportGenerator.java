package tar.report;

import com.atlassian.jira.rest.client.api.domain.BasicIssue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tar.configuration.ConfigReader;
import tar.model.Attachment;
import tar.model.EmailReportConfig;
import tar.model.alert.AlertConfig;
import tar.model.jira.IssueConfig;
import tar.model.jira.IssueData;
import tar.model.query.QueryResult;
import tar.model.report.ReportConfig;
import tar.model.report.ReportResult;
import tar.service.EmailService;
import tar.service.JiraService;
import tar.service.QueryService;

import javax.mail.MessagingException;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Class that generates the reports.
 */
public class ReportGenerator {

    private static final Logger LOGGER = LogManager.getLogger(ReportGenerator.class);
    private final ConfigReader configReader;
    private final JiraService jiraService;
    private final QueryService queryService;
    private final IssueDataTransformer issueDataTransformer;
    private final EmailService emailService;
    private final String emailSender;

    public ReportGenerator(ConfigReader configReader,
                           QueryService queryService,
                           IssueDataTransformer issueDataTransformer,
                           JiraService jiraService,
                           EmailService emailService,
                           String emailSender) {
        this.configReader = configReader;
        this.jiraService = jiraService;
        this.queryService = queryService;
        this.issueDataTransformer = issueDataTransformer;
        this.emailService = emailService;
        this.emailSender = emailSender;
    }

    /**
     * Generates multiple reports.
     */
    public void generateAll(ReportType... reportTypes) {
        List<ReportResult> reportResults = Stream.of(reportTypes)
            .map(this::generate)
            .collect(Collectors.toList());

        createAlertIssue(reportResults);
    }

    private ReportResult generate(ReportType reportType) {
        ReportResult reportResult = new ReportResult(reportType);
        String reportTypeName = reportType.getName();
        try {
            ReportConfig reportConfig = configReader.readReportConfig(reportTypeName);
            QueryResult queryResult;
            LOGGER.info("Starting to create the report {}", reportTypeName);
            queryResult = queryService.executeQuery(reportConfig.getQuery());

            if (queryResult.getRows().isEmpty()) {
                LOGGER.info("No row found, report won't be generated: {}", reportTypeName);
            } else {
                if (reportConfig.getIssue() != null) {
                    createJiraIssue(reportConfig, reportType, queryResult);
                }
                if (reportConfig.getEmailConfig() != null) {
                    createEmail(reportConfig, queryResult);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error generating the report:" + reportTypeName, e);
            return reportResult.withFail();
        }
        return reportResult.withSuccess();
    }

    private void createEmail(ReportConfig reportConfig, QueryResult queryResult) throws MessagingException, IOException {
        EmailReportConfig emailReportConfig = reportConfig.getEmailConfig();
        if (emailReportConfig == null) {
            throw new IllegalArgumentException("Can't create email with empty email config!");
        }
        Attachment attachment = emailReportConfig.getAttachment();
        File file = null;
        if (attachment != null) {
            String format = attachment.getFormat();
            String csvString = QueryResultTransformer.transform(format, queryResult);
            file = createFile(attachment.getName(), format, csvString);
        }

        String emailBody = emailReportConfig.getBody();
        if(emailReportConfig.getBodyTemplate() != null) {
            emailBody = QueryResultTransformer.transformTemplate(queryResult, emailReportConfig.getBodyTemplate());
        }
        emailService.sendEmail(
            emailReportConfig.getRecipients(), emailSender,
            emailReportConfig.getSubject(), emailBody,
            file, emailReportConfig.getContentType());
    }

    private File createFile(String fileName, String format, String content) throws IOException {
        Path path = Paths.get("/tmp/" + fileName + "." + format);
        Files.write(path, content.getBytes());
        return path.toFile();
    }

    private void createJiraIssue(ReportConfig reportConfig, ReportType reportType, QueryResult queryResult) throws URISyntaxException, IOException {
        IssueConfig issueConfig = reportConfig.getIssue();
        if (reportType == ReportType.ORDERS_STUCK_IN_INGRAM_MICRO_WAREHOUSE_DIRECT
            || reportType == ReportType.ORDERS_STUCK_IN_INGRAM_MICRO_WAREHOUSE_TRADEBYTE) {
            String summary = issueConfig.getSummary();
            issueConfig.setSummary(summary + ", " + queryResult.getRows().size());
            String atlassianAccountId = System.getenv("ATLASSIAN_ACCOUNT_ID_FOR_BEING_NOTIFIED_OF_ORDER_STUCK_IN_INGRAM_MICRO");
            if(atlassianAccountId != null) {
                IssueConfig.Description description = issueConfig.getDescription();
                description.setText(description.getText() + "\n\n" + "cc:  [~accountid:" + atlassianAccountId + "]");
            }
        }
        IssueData issueData = issueDataTransformer.transformQueryResult(queryResult, issueConfig);
        BasicIssue basicIssue = jiraService.createIssue(issueData);
        LOGGER.info("Created issue {}  for the report {}", basicIssue.getKey(), reportType.getName());

        //adding Jira Issue watchers if they are available in report config yaml
        //It is important that BSE Automation user has "Manage Watchers" permissions for Jira project that ticket is created in
        //Important Jira config https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issue-watchers/#api-rest-api-3-issue-issueidorkey-watchers-post
        jiraService.addWatchers(basicIssue.getKey(), reportConfig);
    }

    /**
     * Generates MISSING_TRADEBYTE_ORDERS_IN_OMS report.
     */
    public void generateMissingTbOrdersInOmsReport(Set<String> tbOrderIds) {
        ReportResult reportResult = new ReportResult(ReportType.MISSING_TRADEBYTE_ORDERS_IN_OMS);
        String reportTypeName = ReportType.MISSING_TRADEBYTE_ORDERS_IN_OMS.getName();
        LOGGER.info("Starting to create a report {}", reportTypeName);
        try {
            ReportConfig reportConfig = configReader.readReportConfig(reportTypeName);
            QueryResult queryResult = queryService.executeQueryWithInClause(
                new ArrayList<>(tbOrderIds),
                reportConfig.getQuery());


            if (queryResult == null || queryResult.getRows() == null) {
                throw new IllegalArgumentException("SQL query result for a report " + reportTypeName + " was null");
            }

            if (queryResult.getRows().size() != tbOrderIds.size()) {
                Set<String> omsOrders = queryResult.getRows().stream()
                    .peek(row -> LOGGER.info("Row from DB: {}", row.toString()))
                    .map(fields -> fields.get(0))
                    .collect(Collectors.toSet());
                LOGGER.info("Found OMS orders: {}", omsOrders.toString());
                tbOrderIds.removeAll(omsOrders);
                LOGGER.info("Not found orders: {}", tbOrderIds.toString());
                IssueData issueData = issueDataTransformer.createIssueData(reportConfig.getIssue(), String.join("\n", tbOrderIds));
                BasicIssue basicIssue = jiraService.createIssue(issueData);
                LOGGER.info("Created issue {}  for a report {}", basicIssue.getKey(), reportTypeName);
            } else {
                LOGGER.info("All orders are in proper state, no alert will be created");
            }
            reportResult.withSuccess();
        } catch (Exception e) {
            LOGGER.error("Error generating a report:" + reportTypeName, e);
            reportResult.withFail();
        }
        createAlertIssue(Collections.singletonList(reportResult));
    }

    /**
     * Generates MISSING_ORDER_STATUS_UPDATES_IN_TRADEBYTE report.
     */
    public void generateMissingOrderStatusInTbReport(Set<String> tbOrderIds) {
        ReportResult reportResult = new ReportResult(ReportType.MISSING_ORDER_STATUS_UPDATES_IN_TRADEBYTE);
        String reportTypeName = ReportType.MISSING_ORDER_STATUS_UPDATES_IN_TRADEBYTE.getName();
        LOGGER.info("Starting to create report {}", reportTypeName);
        try {
            ReportConfig reportConfig = configReader.readReportConfig(reportTypeName);
            QueryResult queryResult = queryService.executeQueryWithInClause(
                    new ArrayList<>(tbOrderIds),
                    reportConfig.getQuery());

            if (queryResult == null) {
                throw new IllegalArgumentException("SQL query result for report " + reportTypeName + " was null!");
            }
            if (queryResult.getRows() != null && !queryResult.getRows().isEmpty()) {
                // mismatch found
                IssueData issueData = issueDataTransformer.transformQueryResult(queryResult, reportConfig.getIssue());
                BasicIssue basicIssue = jiraService.createIssue(issueData);
                LOGGER.info("Created issue {}  for report {}", basicIssue.getKey(), reportTypeName);
            }
            reportResult.withSuccess();
        } catch (Exception e) {
            LOGGER.error("Error generating report:" + reportTypeName, e);
            reportResult.withFail();
        }
        createAlertIssue(Collections.singletonList(reportResult));
    }

    private void createAlertIssue(List<ReportResult> reportResults) {
        List<String> failedReports =
            reportResults
                .stream()
                .filter(reportResult -> !reportResult.getSuccess())
                .map(reportResult -> reportResult.getReportType().name())
                .collect(Collectors.toList());

        if (!failedReports.isEmpty()) {
            try {
                AlertConfig alertConfig = configReader.readAlertConfig("FailedReportCreation");
                IssueData issueData = issueDataTransformer.transformStringList(failedReports, alertConfig.getIssue());
                BasicIssue basicIssue = jiraService.createIssue(issueData);
                LOGGER.info("Created issue {}  for the the failure of the reports", basicIssue.getKey());
            } catch (Exception e) {
                LOGGER.error("Error generating the failed report issue:" + failedReports, e);
            }
        }
    }
}
