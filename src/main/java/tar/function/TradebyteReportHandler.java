package tar.function;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tar.exception.ReportException;
import tar.report.ReportGenerator;
import tar.report.ReportGeneratorFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Should be triggered by S3 event when Tradebyte report is saved to TBC S3 bucket.
 */
public class TradebyteReportHandler implements RequestHandler<S3Event, String> {

    private static final Logger LOG = LogManager.getLogger(TradebyteReportHandler.class);
    private static final String TB_ORDER_PREFIX = "TB";
    private static final int NOT_COMPLETED_ORDERS_CHANNEL_ID_COLUMN = 0;
    private static final int NOT_COMPLETED_ORDERS_COLUMNS = 6;
    private static final int NOT_COMPLETED_ORDERS_ORDER_ID_COLUMN = 4;
    private static final int NOT_COMPLETED_ORDERS_ORDER_DATE_COLUMN = 2;
    private static final int ORDERS_OLDER_THAN_DAYS = 1;
    private static final int UPDATES_OLDER_THAN_DAYS = 2;

    // custom selection not to include some obsolete orders/channels
    private static final LocalDate CUTOFF_DATE = LocalDate.now().minusYears(1);
    private static final String CHANNEL_TO_IGNORE = "208";


    private final ReportGenerator reportGenerator = new ReportGeneratorFactory().create();
    private final AmazonS3 s3Client = AmazonS3ClientBuilder.defaultClient();

    @Override
    public String handleRequest(S3Event s3Event, Context context) {
        S3EventNotification.S3EventNotificationRecord record = s3Event.getRecords().get(0);
        String srcBucket = record.getS3().getBucket().getName();
        String srcKey = record.getS3().getObject().getUrlDecodedKey();
        LOG.info("Received event for S3 key: {} for bucket: {}", srcKey, srcBucket);

        Map<String, LocalDate> orderIdToDateOrdered = new HashMap<>();

        try (
            final S3Object s3Object = s3Client.getObject(new GetObjectRequest(srcBucket, srcKey));
            final InputStreamReader streamReader = new InputStreamReader(s3Object.getObjectContent(), StandardCharsets.UTF_8);
            final BufferedReader reader = new BufferedReader(streamReader)
        ) {
            reader.readLine(); // skip the header
            String line;
            while ((line = reader.readLine()) != null) {
                // process the line
                String[] columns = line.split(";");
                if (columns.length != NOT_COMPLETED_ORDERS_COLUMNS) {
                    throw new IllegalArgumentException("Unexpected number of fields in string " + line + "from file " + srcKey);
                }
                String orderId = columns[NOT_COMPLETED_ORDERS_ORDER_ID_COLUMN];
                LocalDate orderDate = LocalDate.parse(columns[NOT_COMPLETED_ORDERS_ORDER_DATE_COLUMN]);
                String channelId = columns[NOT_COMPLETED_ORDERS_CHANNEL_ID_COLUMN];

                if(!isToIgnore(channelId, orderDate)) {
                    orderIdToDateOrdered.put(TB_ORDER_PREFIX + orderId, orderDate);
                }
            }
        } catch (IOException e) {
            throw new ReportException("Exception while reading file " + srcKey + " from bucket " + srcBucket + "!", e);
        }

        LocalDate lateOrderMaxDate = LocalDate.now().minusDays(ORDERS_OLDER_THAN_DAYS);
        Set<String> ordersToCheckInOms = orderIdToDateOrdered.entrySet().stream()
            .filter(entry -> entry.getValue().isBefore(lateOrderMaxDate))
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
        if (!ordersToCheckInOms.isEmpty()) {
            reportGenerator.generateMissingTbOrdersInOmsReport(ordersToCheckInOms);
        }


        LocalDate lateUpdateMaxDate = LocalDate.now().minusDays(UPDATES_OLDER_THAN_DAYS);
        Set<String> orderUpdatesToCheckInOms = orderIdToDateOrdered.entrySet().stream()
            .filter(entry -> entry.getValue().isBefore(lateUpdateMaxDate))
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
        if (!orderUpdatesToCheckInOms.isEmpty()) {
            reportGenerator.generateMissingOrderStatusInTbReport(orderUpdatesToCheckInOms);
        }

        return "OK";
    }

    // custom cutoff for orders we can ignore.
    private boolean isToIgnore(String channelId, LocalDate orderDate){
        return channelId.equals(CHANNEL_TO_IGNORE) || orderDate.isBefore(CUTOFF_DATE);
    }
}
