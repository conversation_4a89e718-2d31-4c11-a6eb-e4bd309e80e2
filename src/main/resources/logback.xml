<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender with JSON encoder for AWS Lambda -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <includeContext>true</includeContext>
            <includeMdc>true</includeMdc>
            <customFields>{"service":"fulfillment-automated-reports"}</customFields>
            <fieldNames>
                <timestamp>@timestamp</timestamp>
                <level>level</level>
                <logger>logger</logger>
                <message>message</message>
                <thread>thread</thread>
            </fieldNames>
        </encoder>
    </appender>

    <!-- Root logger configuration -->
    <root level="DEBUG">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>
