<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bestseller</groupId>
    <artifactId>fulfillment-automated-reports</artifactId>
    <version>1.0-SNAPSHOT</version>

    <name>Fulfillment Automated Reports</name>
    <description>BESTSELLER Fulfillment Automated Reports</description>
    <packaging>jar</packaging>
    <distributionManagement>
          <repository>
          <id>logistics-commons</id>
          <name>GitHub Packages</name>
          <url>https://maven.pkg.github.com/bestseller-ecom/logistics-commons</url>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
    </distributionManagement>

    <properties>
        <bse-checkstyle.version>1.0.149</bse-checkstyle.version>
        <java.version>1.8</java.version>
        <maven.checkstyle.plugin.version>2.17</maven.checkstyle.plugin.version>
        <maven.plugins.plugin.version>2.22.2</maven.plugins.plugin.version>
        <maven.compiler.plugin.version>3.7.0</maven.compiler.plugin.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <aws-lambda-java-core.version>1.2.1</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>3.7.0</aws-lambda-java-events.version>
        <aws-lambda-java-slf4j.version>1.0.0</aws-lambda-java-slf4j.version>
        <aws-java-sdk-bom.version>1.12.39</aws-java-sdk-bom.version>
        <javax-mail.version>1.6.2</javax-mail.version>
        <jira-rest-java-client-core.version>5.2.2</jira-rest-java-client-core.version>
        <fugue.version>4.7.2</fugue.version>
        <jackson-dataformat-yaml.version>2.12.0</jackson-dataformat-yaml.version>
        <jackson-databind.version>2.12.0</jackson-databind.version>
        <mysql-connector-java.version>8.0.22</mysql-connector-java.version>
        <apache-commons-io.version>2.8.0</apache-commons-io.version>
        <apache-commons-lang3.version>3.11</apache-commons-lang3.version>
        <commons-codec.version>1.15</commons-codec.version>
        <org.apache.httpcomponents.version>4.5.13</org.apache.httpcomponents.version>
        <joda-time.version>2.10.8</joda-time.version>
        <logback.version>1.2.12</logback.version>
        <slf4j.version>1.7.36</slf4j.version>
        <logstash-logback-encoder.version>7.4</logstash-logback-encoder.version>
        <jsonschema2pojo-maven-plugin.version>1.0.2</jsonschema2pojo-maven-plugin.version>
        <junit-jupiter-api.version>5.7.0</junit-jupiter-api.version>
        <mockito.version>2.0.2-beta</mockito.version>
        <org.powermock.version>1.7.4</org.powermock.version>
        <awaitility.version>4.0.3</awaitility.version>
        <spring.test.version>5.3.30</spring.test.version>
        <skipUnitTests>false</skipUnitTests>
    </properties>
    <repositories>
        <repository>
            <id>atlassian-public</id>
            <url>https://maven.atlassian.com/repository/public</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
        </repository>
    </repositories>
    <build>
        <plugins>
            <!-- package for the artifact that will be uploaded to aws lambda  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- For running integration tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.22.2</version>
                <executions>
                    <execution>
                        <id>default</id>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>**/FunctionalTestSuite.java</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven.checkstyle.plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.bestseller</groupId>
                            <artifactId>bse-checkstyle</artifactId>
                            <version>${bse-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>com/bestseller/checkstyle.xml</configLocation>
                        <suppressionsLocation>com/bestseller/suppressions.xml</suppressionsLocation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    </configuration>
                </plugin>

                <!-- For running unit tests -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.plugins.plugin.version}</version>
                    <configuration>
                        <skipTests>${skipUnitTests}</skipTests>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson-dataformat-yaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>${aws-java-sdk-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${org.apache.httpcomponents.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <!-- Necessary for AWS-->
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>${javax-mail.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${apache-commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${apache-commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.atlassian.jira</groupId>
            <artifactId>jira-rest-java-client-core</artifactId>
            <version>${jira-rest-java-client-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.atlassian.fugue</groupId>
            <artifactId>fugue</artifactId>
            <version>${fugue.version}</version>
        </dependency>
        <!-- Logging dependencies -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-jupiter-api.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- AWS-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-secretsmanager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ses</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
            <version>${aws-lambda-java-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-events</artifactId>
            <version>${aws-lambda-java-events.version}</version>
        </dependency>

        <!-- /AWS-->

        <!-- Tests -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${org.powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>${org.powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>${awaitility.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- /Tests -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.test.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
